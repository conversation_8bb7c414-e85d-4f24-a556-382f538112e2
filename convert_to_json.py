#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将2025.txt文件中的数据转换为JSON格式
"""

import json
import re
import os
import glob
from datetime import datetime
from docx import Document

def extract_ip_ban_count_from_docx(file_path):
    """
    从Word文档中提取IP地址数量

    Args:
        file_path (str): Word文档路径

    Returns:
        int: IP地址数量，如果未找到则返回0
    """
    try:
        doc = Document(file_path)

        # 搜索包含IP封禁相关的文本，使用更宽泛的正则表达式
        for paragraph in doc.paragraphs:
            text = paragraph.text
            # 匹配多种可能的IP封禁描述模式
            patterns = [
                r'封禁.*?(\d+).*?IP',  # 封禁...数字...IP
                r'IP.*?封禁.*?(\d+)',  # IP...封禁...数字
                r'监测发现异常(\d+)个IP地址',  # 原有模式
                r'发现.*?(\d+).*?IP.*?地址',  # 发现...数字...IP...地址
                r'异常.*?(\d+).*?IP',  # 异常...数字...IP
                r'(\d+).*?个.*?IP.*?地址',  # 数字...个...IP...地址
            ]

            for pattern in patterns:
                match = re.search(pattern, text)
                if match:
                    return int(match.group(1))

        # 如果段落中没找到，检查表格
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    text = cell.text
                    # 使用相同的多种模式匹配
                    patterns = [
                        r'封禁.*?(\d+).*?IP',  # 封禁...数字...IP
                        r'IP.*?封禁.*?(\d+)',  # IP...封禁...数字
                        r'监测发现异常(\d+)个IP地址',  # 原有模式
                        r'发现.*?(\d+).*?IP.*?地址',  # 发现...数字...IP...地址
                        r'异常.*?(\d+).*?IP',  # 异常...数字...IP
                        r'(\d+).*?个.*?IP.*?地址',  # 数字...个...IP...地址
                    ]

                    for pattern in patterns:
                        match = re.search(pattern, text)
                        if match:
                            return int(match.group(1))

        return 0
    except Exception as e:
        print(f"读取文档 {file_path} 时出错: {e}")
        return 0

def get_daily_ip_ban_count(date_str):
    """
    获取指定日期的IP封禁总数

    Args:
        date_str (str): 日期字符串，格式为YYYY-MM-DD

    Returns:
        int: 当日IP封禁总数
    """
    # 将日期格式转换为文件名格式 (例如: 2025-08-10 -> 8.10)
    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
    file_date = f"{date_obj.month}.{date_obj.day}"

    # 查找对应日期的三个Word文档
    patterns = [
        f"北京市药品监督管理局网络安全运维保障工作早报{file_date}.docx",
        f"北京市药品监督管理局网络安全运维保障工作午报{file_date}.docx",
        f"北京市药品监督管理局网络安全运维保障工作晚报{file_date}.docx"
    ]

    total_count = 0
    for pattern in patterns:
        files = glob.glob(pattern)
        for file_path in files:
            if os.path.exists(file_path):
                count = extract_ip_ban_count_from_docx(file_path)
                total_count += count
                print(f"从 {file_path} 提取到 {count} 个IP地址")

    return total_count

def parse_2025_txt(file_path):
    """
    解析2025.txt文件，提取两个数据表

    Args:
        file_path (str): 文件路径

    Returns:
        dict: 包含data1和data2的字典
    """

    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    lines = content.split('\n')

    # 获取当前日期
    current_date = datetime.now()
    report_date = f"{current_date.year}年{current_date.month}月{current_date.day}日"

    # 初始化结果字典
    result = {
        "report_date": report_date,
        "data1": {
            "title": "当天攻击统计",
            "type": "attack_type_daily",
            "data": []
        },
        "data2": {
            "title": "累计攻击统计",
            "regions": [],
            "attack_types": []
        }
    }

    current_section = None
    cumulative_subsection = "regions"  # 累计部分的子部分：regions 或 attack_types

    for i, line in enumerate(lines):
        line = line.strip()

        # 识别主要部分
        if line == "当天：":
            current_section = "daily"
            continue
        elif line == "累计：":
            current_section = "cumulative"
            cumulative_subsection = "regions"  # 重置为地域数据
            continue
        elif line.startswith("排序"):
            # 跳过表头
            continue
        elif not line:
            # 空行处理：在累计部分遇到空行时，切换到攻击类型数据
            if current_section == "cumulative" and cumulative_subsection == "regions":
                cumulative_subsection = "attack_types"
            continue

        # 解析数据行
        if current_section == "daily":
            # 解析当天数据
            parts = line.split('\t')
            if len(parts) >= 3 and parts[0].isdigit():
                result["data1"]["data"].append({
                    "rank": int(parts[0]),
                    "attack_type": parts[1],
                    "count": int(parts[2])
                })

        elif current_section == "cumulative":
            parts = line.split('\t')
            if len(parts) >= 3 and parts[0].isdigit():
                if cumulative_subsection == "regions":
                    # 地域数据
                    result["data2"]["regions"].append({
                        "rank": int(parts[0]),
                        "region": parts[1],
                        "count": int(parts[2])
                    })
                elif cumulative_subsection == "attack_types":
                    # 攻击类型数据
                    result["data2"]["attack_types"].append({
                        "rank": int(parts[0]),
                        "attack_type": parts[1],
                        "count": int(parts[2])
                    })

    return result

def calculate_attack_level(total_attacks):
    """
    根据攻击总数计算攻击等级

    Args:
        total_attacks (int): 攻击总数

    Returns:
        str: 攻击等级描述
    """
    if total_attacks < 100:
        return "小"
    elif 100 <= total_attacks <= 200:
        return "正常"
    elif 400 <= total_attacks <= 600:
        return "略高"
    elif total_attacks > 600:
        return "高"
    else:
        # 200-400之间，可以归为正常偏高
        return "正常"

def update_month_attacks(existing_data, current_total):
    """
    更新month_attacks数据，追加当日攻击数据

    Args:
        existing_data (dict): 现有的JSON数据
        current_total (int): 当日攻击总数

    Returns:
        list: 更新后的month_attacks数组
    """
    # 获取当前日期（简化格式）
    current_date = datetime.now().strftime("%Y-%m-%d")

    # 如果month_attacks不存在，创建它
    if "month_attacks" not in existing_data:
        existing_data["month_attacks"] = []

    month_attacks = existing_data["month_attacks"]

    # 检查当日数据是否已存在
    existing_dates = [item["date"] for item in month_attacks]

    if current_date in existing_dates:
        # 更新现有数据
        for item in month_attacks:
            if item["date"] == current_date:
                item["count"] = current_total
                print(f"更新现有数据: {current_date} -> {current_total}")
                break
    else:
        # 追加新数据
        month_attacks.append({
            "date": current_date,
            "count": current_total
        })
        print(f"追加新数据: {current_date} -> {current_total}")

    # 按日期排序
    month_attacks.sort(key=lambda x: x["date"])

    return month_attacks

def update_ip_ban(existing_data):
    """
    更新ip_ban数据，添加或更新当日的IP封禁数量

    Args:
        existing_data (dict): 现有的JSON数据

    Returns:
        list: 更新后的ip_ban数组
    """
    # 获取当前日期
    current_date = datetime.now().strftime("%Y-%m-%d")

    # 如果ip_ban不存在，创建它
    if "ip_ban" not in existing_data:
        existing_data["ip_ban"] = []

    ip_ban = existing_data["ip_ban"]

    # 获取当日IP封禁数量
    current_ip_ban_count = get_daily_ip_ban_count(current_date)

    # 检查当日数据是否已存在
    existing_dates = [item["date"] for item in ip_ban]

    if current_date in existing_dates:
        # 更新现有数据
        for item in ip_ban:
            if item["date"] == current_date:
                item["count"] = current_ip_ban_count
                print(f"更新现有IP封禁数据: {current_date} -> {current_ip_ban_count}")
                break
    else:
        # 追加新数据
        ip_ban.append({
            "date": current_date,
            "count": current_ip_ban_count
        })
        print(f"添加新IP封禁数据: {current_date} -> {current_ip_ban_count}")

    # 按日期排序
    ip_ban.sort(key=lambda x: x["date"])

    return ip_ban

def main():
    """主函数"""
    try:
        # 解析文件
        file_path = "2025.txt"
        output_file = "2025_data.json"

        # 尝试读取现有JSON文件
        existing_data = {}
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
            print("读取现有JSON文件成功")
        except FileNotFoundError:
            print("JSON文件不存在，将创建新文件")
        except Exception as e:
            print(f"读取现有JSON文件时出错: {e}")

        # 解析新的txt数据
        data = parse_2025_txt(file_path)

        # 计算当日攻击总数和等级
        daily_total = sum(item['count'] for item in data['data1']['data'])
        attack_level = calculate_attack_level(daily_total)

        # 添加攻击等级信息到data1
        data['data1']['total_attacks'] = daily_total
        data['data1']['attack_level'] = attack_level

        # 更新month_attacks数据
        data['month_attacks'] = update_month_attacks(existing_data, daily_total)

        # 更新ip_ban数据
        data['ip_ban'] = update_ip_ban(existing_data)

        # 转换为JSON字符串
        json_string = json.dumps(data, ensure_ascii=False, indent=2)

        # 输出JSON字符串
        print("转换后的JSON数据：")
        print(json_string)

        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(json_string)

        print(f"\nJSON数据已保存到文件: {output_file}")

        # 显示统计信息
        print(f"\n数据统计：")
        print(f"当天攻击类型数量: {len(data['data1']['data'])}")
        print(f"当天攻击总数: {daily_total}")
        print(f"攻击等级: {attack_level}")
        print(f"累计地域数量: {len(data['data2']['regions'])}")
        print(f"累计攻击类型数量: {len(data['data2']['attack_types'])}")
        print(f"月度攻击数据天数: {len(data['month_attacks'])}")
        print(f"IP封禁数据天数: {len(data['ip_ban'])}")
        if data['ip_ban']:
            latest_ip_ban = data['ip_ban'][-1]
            print(f"最新IP封禁数量: {latest_ip_ban['count']} (日期: {latest_ip_ban['date']})")

    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
    except Exception as e:
        print(f"处理文件时发生错误: {e}")

if __name__ == "__main__":
    main()
