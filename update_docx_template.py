#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用模板语法更新docx文件，保持原有格式
"""

import json
import re
import os
from datetime import datetime, timedelta
from docx import Document
from docx.text.paragraph import Paragraph
from docx.table import Table, _Cell
from docx.text.run import Run
from docx.shared import Inches
import matplotlib.pyplot as plt
import matplotlib.dates as mdates

def load_json_data(json_file):
    """
    加载JSON数据
    
    Args:
        json_file (str): JSON文件路径
        
    Returns:
        dict: JSON数据
    """
    with open(json_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def calculate_total(data1):
    """
    计算data1中所有攻击次数的总和

    Args:
        data1 (dict): data1数据

    Returns:
        int: 总攻击次数
    """
    total = 0
    for item in data1['data']:
        total += item['count']
    return total

def calculate_cumulative_total(data2):
    """
    计算data2中累计攻击类型的总和

    Args:
        data2 (dict): data2数据

    Returns:
        int: 累计攻击次数
    """
    total = 0
    for item in data2['attack_types']:
        total += item['count']
    return total

def replace_template_in_run(run, template_vars):
    """
    在Run对象中替换模板变量，保持格式
    
    Args:
        run: Run对象
        template_vars (dict): 模板变量字典
    """
    if run.text:
        original_text = run.text
        new_text = original_text
        
        # 替换所有模板变量
        for var_name, var_value in template_vars.items():
            pattern = r'\{\{\s*' + re.escape(var_name) + r'\s*\}\}'
            new_text = re.sub(pattern, str(var_value), new_text)
        
        if new_text != original_text:
            run.text = new_text
            print(f"替换模板变量：{original_text} -> {new_text}")

def replace_template_in_paragraph(paragraph, template_vars):
    """
    在段落中替换模板变量，保持格式
    
    Args:
        paragraph: Paragraph对象
        template_vars (dict): 模板变量字典
    """
    for run in paragraph.runs:
        replace_template_in_run(run, template_vars)

def replace_template_in_cell(cell, template_vars):
    """
    在单元格中替换模板变量，保持格式
    
    Args:
        cell: Cell对象
        template_vars (dict): 模板变量字典
    """
    for paragraph in cell.paragraphs:
        replace_template_in_paragraph(paragraph, template_vars)

def process_table_template(table, data1, data2=None, month_attacks=None, ip_ban=None):
    """
    处理表格模板，填充数据

    Args:
        table: Table对象
        data1 (dict): data1数据（当日攻击数据）
        data2 (dict): data2数据（累计攻击数据，可选）
        month_attacks (list): 月度攻击数据，可选
        ip_ban (list): IP封禁数据，可选
    """
    # 检查表格类型
    is_cumulative_table = False
    is_month_attacks_table = False
    is_ip_ban_table = False

    for row in table.rows:
        for cell in row.cells:
            if "cumulative_attack" in cell.text:
                is_cumulative_table = True
                break
            elif "month_attacks" in cell.text:
                is_month_attacks_table = True
                break
            elif "ip_ban" in cell.text:
                is_ip_ban_table = True
                break
        if is_cumulative_table or is_month_attacks_table or is_ip_ban_table:
            break

    if is_ip_ban_table and ip_ban:
        # 处理IP封禁表格
        data_source = ip_ban
        template_var_prefix = 'ip_ban'
        print("处理IP封禁表格")
    elif is_month_attacks_table and month_attacks:
        # 处理月度攻击表格
        data_source = month_attacks
        template_var_prefix = 'month_attacks'
        print("处理月度攻击表格")
    elif is_cumulative_table and data2:
        # 处理累计攻击表格
        data_source = data2['attack_types']
        total_count = calculate_cumulative_total(data2)
        template_var_prefix = 'cumulative_attack'
        total_var_name = 'cumulative_attacks'
        print("处理累计攻击表格")
    else:
        # 处理当日攻击表格
        data_source = data1['data']
        total_count = calculate_total(data1)
        template_var_prefix = 'attack'
        total_var_name = 'total_attacks'
        print("处理当日攻击表格")

    print(f"数据源包含 {len(data_source)} 个数据项")

    # 查找包含模板语法的行
    template_row_index = None

    for i, row in enumerate(table.rows):
        for cell in row.cells:
            if "{{" in cell.text and template_var_prefix in cell.text:
                template_row_index = i
                print(f"找到模板行，索引：{i}")
                break
        if template_row_index is not None:
            break

    if template_row_index is None:
        print("未找到表格模板行")
        return

    # 删除模板行之后的所有行
    rows_to_remove = []
    for i in range(template_row_index, len(table.rows)):
        rows_to_remove.append(i)

    for i in reversed(rows_to_remove):
        table._element.remove(table.rows[i]._element)

    # 为每个数据项添加新行
    if is_ip_ban_table:
        # IP封禁表格：显示日期和封禁数
        for idx, item in enumerate(data_source):
            new_row = table.add_row()
            print(f"添加行 {idx + 1}: {item['date']} - {item['count']}")

            # 填充数据
            new_row.cells[0].text = item['date']
            new_row.cells[1].text = str(item['count'])

        print(f"IP封禁表格重建完成，共 {len(data_source)} 个数据行")

    elif is_month_attacks_table:
        # 月度攻击表格：显示日期和攻击数
        for idx, item in enumerate(data_source):
            new_row = table.add_row()
            print(f"添加行 {idx + 1}: {item['date']} - {item['count']}")

            # 填充数据
            new_row.cells[0].text = item['date']
            new_row.cells[1].text = str(item['count'])

        print(f"月度攻击表格重建完成，共 {len(data_source)} 个数据行")

    else:
        # 攻击类型表格：显示攻击类型和攻击数
        for idx, item in enumerate(data_source):
            new_row = table.add_row()
            print(f"添加行 {idx + 1}: {item['attack_type']} - {item['count']}")

            # 填充数据
            new_row.cells[0].text = item['attack_type']
            new_row.cells[1].text = str(item['count'])

        # 添加小计行
        subtotal_row = table.add_row()
        subtotal_row.cells[0].text = "小计"
        subtotal_row.cells[1].text = str(total_count)
        print(f"添加小计行：{total_count}")

        print(f"攻击类型表格重建完成，共 {len(data_source)} 个数据行 + 1 个小计行")

def generate_attack_trend_chart(month_attacks, report_date, output_path="attack_trend.png"):
    """
    生成攻击趋势折线图

    Args:
        month_attacks (list): 月度攻击数据
        report_date (str): 报告日期，格式如"2025年8月11日"
        output_path (str): 图片输出路径

    Returns:
        str: 生成的图片文件路径
    """
    try:
        # 解析报告日期
        import re
        date_match = re.match(r'(\d{4})年(\d{1,2})月(\d{1,2})日', report_date)
        if not date_match:
            print(f"无法解析报告日期: {report_date}")
            return None

        year, month, day = date_match.groups()
        report_datetime = datetime(int(year), int(month), int(day))

        # 计算15天范围（以报告日期为中心，前后各7天）
        start_date = report_datetime - timedelta(days=7)
        end_date = report_datetime + timedelta(days=7)

        # 过滤数据，只保留15天范围内的数据
        filtered_data = []
        for item in month_attacks:
            item_date = datetime.strptime(item['date'], '%Y-%m-%d')
            if start_date <= item_date <= end_date:
                filtered_data.append(item)

        if not filtered_data:
            print("没有找到15天范围内的数据")
            return None

        # 按日期排序
        filtered_data.sort(key=lambda x: x['date'])

        # 提取日期和攻击数
        dates = [datetime.strptime(item['date'], '%Y-%m-%d') for item in filtered_data]
        counts = [item['count'] for item in filtered_data]

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 6))

        # 绘制折线图
        ax.plot(dates, counts, marker='o', linewidth=2, markersize=6, color='#1f77b4')

        # 在每个点上标注数值
        for i, (date, count) in enumerate(zip(dates, counts)):
            ax.annotate(str(count), (date, count), textcoords="offset points",
                       xytext=(0,10), ha='center', fontsize=10)

        # 设置标题和标签
        ax.set_title('攻击趋势图', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('日期', fontsize=12)
        ax.set_ylabel('攻击数（次）', fontsize=12)

        # 格式化x轴日期
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=1))

        # 旋转x轴标签
        plt.xticks(rotation=45)

        # 设置网格
        ax.grid(True, alpha=0.3)

        # 调整布局
        plt.tight_layout()

        # 保存图片
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"攻击趋势图已生成: {output_path}")
        print(f"图表包含 {len(filtered_data)} 天的数据")

        return output_path

    except Exception as e:
        print(f"生成攻击趋势图时发生错误: {e}")
        return None

def generate_ip_ban_chart(ip_ban, report_date, output_path="ip_ban_trend.png"):
    """
    生成IP封禁趋势折线图

    Args:
        ip_ban (list): IP封禁数据
        report_date (str): 报告日期，格式如"2025年8月11日"
        output_path (str): 图片输出路径

    Returns:
        str: 生成的图片文件路径
    """
    try:
        if not ip_ban:
            print("没有IP封禁数据")
            return None

        # 按日期排序
        sorted_data = sorted(ip_ban, key=lambda x: x['date'])

        # 提取日期和封禁数
        dates = [datetime.strptime(item['date'], '%Y-%m-%d') for item in sorted_data]
        counts = [item['count'] for item in sorted_data]

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 6))

        # 绘制折线图
        ax.plot(dates, counts, marker='o', linewidth=2, markersize=6, color='#1f77b4')

        # 在每个点上标注数值
        for i, (date, count) in enumerate(zip(dates, counts)):
            ax.annotate(str(count), (date, count), textcoords="offset points",
                       xytext=(0,10), ha='center', fontsize=10)

        # 设置标题和标签
        ax.set_title('IP封禁数量', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('日期', fontsize=12)
        ax.set_ylabel('IP封禁数量（个）', fontsize=12)

        # 格式化x轴日期
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=1))

        # 旋转x轴标签
        plt.xticks(rotation=45)

        # 设置网格
        ax.grid(True, alpha=0.3)

        # 调整布局
        plt.tight_layout()

        # 保存图片
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"IP封禁趋势图已生成: {output_path}")
        print(f"图表包含 {len(sorted_data)} 天的数据")

        return output_path

    except Exception as e:
        print(f"生成IP封禁趋势图时发生错误: {e}")
        return None

def generate_region_attack_chart(regions, report_date, output_path="region_attack_chart.png"):
    """
    生成地区攻击分布横向条形图

    Args:
        regions (list): 地区攻击数据
        report_date (str): 报告日期，格式如"2025年8月11日"
        output_path (str): 图片输出路径

    Returns:
        str: 生成的图片文件路径
    """
    try:
        if not regions:
            print("没有地区攻击数据")
            return None

        # 分类统计
        beijing_count = 0
        non_beijing_china_count = 0
        non_china_count = 0

        # 详细地区数据用于图表显示
        beijing_regions = []
        non_beijing_regions = []
        non_china_regions = []

        for region in regions:
            region_name = region['region']
            count = region['count']

            if '北京' in region_name:
                beijing_count += count
                beijing_regions.append((region_name, count))
            elif '中国' in region_name:
                non_beijing_china_count += count
                non_beijing_regions.append((region_name, count))
            else:
                non_china_count += count
                non_china_regions.append((region_name, count))

        # 准备图表数据 - 按攻击次数排序显示前10个地区
        all_regions = [(r['region'], r['count']) for r in regions[:10]]
        all_regions.reverse()  # 反转以便最大值在顶部

        region_names = [r[0] for r in all_regions]
        counts = [r[1] for r in all_regions]

        # 为每个地区分配颜色，使用朴素沉稳的配色
        colors = []
        for region_name, _ in all_regions:
            if '北京' in region_name:
                colors.append('#fc8d62')  # 深棕色 - 北京
            elif '中国' in region_name:
                colors.append('#8da0cb')  # 钢蓝色 - 非北京（中国）
            else:
                colors.append('#66c2a5')  # 深灰色 - 非中国

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 8))

        # 绘制横向条形图
        bars = ax.barh(region_names, counts, color=colors)

        # 在每个条形上标注数值
        for i, (bar, count) in enumerate(zip(bars, counts)):
            ax.text(bar.get_width() + max(counts) * 0.01, bar.get_y() + bar.get_height()/2,
                   str(count), ha='left', va='center', fontsize=10)

        # 设置标题和标签
        ax.set_title('不同地区攻击次数统计', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('攻击次数', fontsize=12)
        ax.set_ylabel('地区', fontsize=12)

        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='#66c2a5', label='非中国'),
            Patch(facecolor='#fc8d62', label='北京'),
            Patch(facecolor='#8da0cb', label='非北京（中国）')
        ]
        ax.legend(handles=legend_elements, loc='lower right')

        # 设置网格
        ax.grid(True, alpha=0.3, axis='x')

        # 调整布局
        plt.tight_layout()

        # 保存图片
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"地区攻击分布图已生成: {output_path}")
        print(f"北京地区攻击次数: {beijing_count}")
        print(f"非北京中国地区攻击次数: {non_beijing_china_count}")
        print(f"非中国地区攻击次数: {non_china_count}")

        return output_path

    except Exception as e:
        print(f"生成地区攻击分布图时发生错误: {e}")
        return None

def update_docx_with_template(template_file, json_file, output_file=None):
    """
    使用模板语法更新docx文件

    Args:
        template_file (str): 模板文件路径
        json_file (str): JSON数据文件路径
        output_file (str): 输出文件路径，如果为None则覆盖原文件
    """
    # 加载数据
    data = load_json_data(json_file)
    data1 = data['data1']
    data2 = data['data2']
    month_attacks = data.get('month_attacks', [])
    ip_ban = data.get('ip_ban', [])
    total_count = calculate_total(data1)
    cumulative_total = calculate_cumulative_total(data2)

    print(f"报告日期：{data['report_date']}")
    print(f"data1数据项数量：{len(data1['data'])}")
    print(f"总攻击次数：{total_count}")
    print(f"data2累计攻击类型数量：{len(data2['attack_types'])}")
    print(f"累计攻击次数：{cumulative_total}")
    print(f"月度攻击数据天数：{len(month_attacks)}")
    print(f"IP封禁数据天数：{len(ip_ban)}")

    # 打开docx文件
    doc = Document(template_file)

    # 准备模板变量
    template_vars = {
        'report_date': data['report_date'],
        'report_date_start': '2025年8月1日',  # 固定开始日期
        'total_attacks': total_count,
        'cumulative_attacks': cumulative_total,
        'attack_level': data['data1'].get('attack_level', '正常')  # 攻击等级
    }

    # 更新所有段落
    for paragraph in doc.paragraphs:
        replace_template_in_paragraph(paragraph, template_vars)

    # 更新所有表格
    for table in doc.tables:
        # 先处理普通模板变量
        for row in table.rows:
            for cell in row.cells:
                replace_template_in_cell(cell, template_vars)

        # 检查是否包含攻击数据模板
        has_attack_template = False
        has_cumulative_template = False
        has_month_attacks_template = False
        has_ip_ban_template = False
        for row in table.rows:
            for cell in row.cells:
                if "{{attack." in cell.text:
                    has_attack_template = True
                elif "{{cumulative_attack." in cell.text:
                    has_cumulative_template = True
                elif "{{month_attacks." in cell.text:
                    has_month_attacks_template = True
                elif "{{ip_ban." in cell.text:
                    has_ip_ban_template = True

        if has_ip_ban_template:
            print("找到IP封禁数据表格模板")
            process_table_template(table, data1, data2, month_attacks, ip_ban)
        elif has_month_attacks_template:
            print("找到月度攻击数据表格模板")
            process_table_template(table, data1, data2, month_attacks)
        elif has_cumulative_template:
            print("找到累计攻击数据表格模板")
            process_table_template(table, data1, data2)
        elif has_attack_template:
            print("找到当日攻击数据表格模板")
            process_table_template(table, data1)

    # 生成攻击趋势图
    if month_attacks:
        chart_path = generate_attack_trend_chart(month_attacks, data['report_date'])
        if chart_path and os.path.exists(chart_path):
            # 在文档中查找"攻击趋势图"文字，在其后插入图片
            for i, paragraph in enumerate(doc.paragraphs):
                if "攻击趋势图" in paragraph.text:
                    # 在该段落后添加新段落并插入图片
                    new_para = doc.add_paragraph()
                    run = new_para.add_run()
                    run.add_picture(chart_path, width=Inches(6))

                    # 将新段落移动到正确位置
                    paragraph_element = paragraph._element
                    new_para_element = new_para._element
                    paragraph_element.getparent().insert(
                        paragraph_element.getparent().index(paragraph_element) + 1,
                        new_para_element
                    )

                    print(f"已在文档中插入攻击趋势图: {chart_path}")
                    break

    # 生成IP封禁趋势图
    if ip_ban:
        ip_ban_chart_path = generate_ip_ban_chart(ip_ban, data['report_date'])
        if ip_ban_chart_path and os.path.exists(ip_ban_chart_path):
            # 查找IP封禁表格，在表格后插入图片
            ip_ban_table_found = False
            for table in doc.tables:
                # 检查是否是IP封禁表格
                for row in table.rows:
                    for cell in row.cells:
                        if "{{ip_ban." in cell.text or ("2025-08-01" in cell.text and any("3" in c.text for c in row.cells)):
                            ip_ban_table_found = True
                            break
                    if ip_ban_table_found:
                        break

                if ip_ban_table_found:
                    # 找到表格在文档中的位置
                    table_element = table._element
                    parent = table_element.getparent()
                    table_index = list(parent).index(table_element)

                    # 在表格后添加新段落并插入图片
                    new_para = doc.add_paragraph()
                    run = new_para.add_run()
                    run.add_picture(ip_ban_chart_path, width=Inches(6))

                    # 将新段落移动到表格后面
                    new_para_element = new_para._element
                    parent.insert(table_index + 1, new_para_element)

                    print(f"已在IP封禁表格后插入趋势图: {ip_ban_chart_path}")
                    break

    # 生成地区攻击分布图
    if data2 and 'regions' in data2:
        region_chart_path = generate_region_attack_chart(data2['regions'], data['report_date'])
        if region_chart_path and os.path.exists(region_chart_path):
            # 在文档中查找"将攻击域划分为北京、非北京、非中国，攻击情况如下图："文字，在其后插入图片
            for i, paragraph in enumerate(doc.paragraphs):
                if "将攻击域划分为" in paragraph.text and "攻击情况如下图" in paragraph.text:
                    # 在该段落后添加新段落并插入图片
                    new_para = doc.add_paragraph()
                    run = new_para.add_run()
                    run.add_picture(region_chart_path, width=Inches(6))

                    # 将新段落移动到正确位置
                    paragraph_element = paragraph._element
                    new_para_element = new_para._element
                    paragraph_element.getparent().insert(
                        paragraph_element.getparent().index(paragraph_element) + 1,
                        new_para_element
                    )

                    print(f"已在文档中插入地区攻击分布图: {region_chart_path}")
                    break

    # 保存文件
    if output_file is None:
        output_file = template_file

    doc.save(output_file)
    print(f"文件已保存：{output_file}")

def main():
    """主函数"""
    try:
        template_file = "重保网络安全总结报告_模板.docx"
        json_file = "2025_data.json"

        # 从JSON文件读取日期来生成文件名
        data = load_json_data(json_file)
        report_date = data['report_date']

        # 将日期格式转换为文件名格式（去掉"年月日"字符，并确保月日为两位数）
        # 例如：2025年8月11日 -> 20250811
        import re
        date_match = re.match(r'(\d{4})年(\d{1,2})月(\d{1,2})日', report_date)
        if date_match:
            year, month, day = date_match.groups()
            date_for_filename = f"{year}{month.zfill(2)}{day.zfill(2)}"
        else:
            # 备用方案：直接替换字符
            date_for_filename = report_date.replace('年', '').replace('月', '').replace('日', '')

        output_file = f"重保网络安全总结报告_{date_for_filename}.docx"

        print("开始使用模板语法更新docx文件...")
        print(f"输出文件名：{output_file}")
        update_docx_with_template(template_file, json_file, output_file)
        print("更新完成！")

    except FileNotFoundError as e:
        print(f"文件未找到：{e}")
    except Exception as e:
        print(f"处理文件时发生错误：{e}")

if __name__ == "__main__":
    main()
